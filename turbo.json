{"$schema": "https://turbo.build/schema.json", "ui": "tui", "globalDependencies": ["**/.env.*local"], "tasks": {"build": {"dependsOn": ["^build", "db:generate"], "outputs": ["dist/**", ".next/**", "!.next/cache/**"], "env": ["GOOGLE_GENERATIVE_AI_API_KEY", "BETTER_AUTH_URL", "BETTER_AUTH_SECRET", "DATABASE_URL"]}, "dev": {"dependsOn": ["db:generate"], "cache": false, "persistent": true, "env": ["GOOGLE_GENERATIVE_AI_API_KEY", "BETTER_AUTH_URL", "BETTER_AUTH_SECRET", "DATABASE_URL"]}, "lint": {"dependsOn": ["^lint"]}, "test": {"dependsOn": ["^build"], "outputs": ["coverage/**"]}, "clean": {"cache": false}, "db:generate": {"cache": false}, "db:push": {"cache": false}, "db:migrate": {"cache": false}, "type-check": {"dependsOn": ["^type-check"]}}}