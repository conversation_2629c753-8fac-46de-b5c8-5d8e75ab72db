// Article types
export type Article = {
  id: string;
  title: string;
  content: string;
  userId: string;
  createdAt: Date | string;
  updatedAt: Date | string;
};

// Note types
export type Note = {
  id: string;
  title: string;
  content: string;
  tags: string[];
  createdAt: Date | string;
  isStarred: boolean;
};

// User types
export type User = {
  id: string;
  email: string;
  name?: string;
  createdAt: Date | string;
  updatedAt: Date | string;
};

// API Response types
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export type ApiResponse<T = any> = {
  data?: T;
  error?: string;
  message?: string;
};

// AI Chat types
export type ChatMessage = {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date | string;
};
