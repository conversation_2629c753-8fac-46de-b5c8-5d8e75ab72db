version: '3'

tasks:
  dev:
    desc: "Run the development server for both frontend and server"
    cmds:
      - pnpm run dev

  build:
    desc: "Build the project with Turborepo"
    cmds:
      - pnpm run build

  deploy:
    desc: "Deploy to staging with Vercel"
    cmds:
      - vc deploy

  deploy:prod:
    desc: "Deploy to production with Vercel"
    cmds:
      - vc deploy --prod

  lint:
    desc: "Lint the project with Turborepo"
    cmds:
      - pnpm run lint

  preview:
    desc: "Preview the built project"
    cmds:
      - pnpm run preview -w packages/app

  install:
    desc: "Install dependencies"
    cmds:
      - pnpm install

  prisma:gen:
    desc: "Generate Prisma client"
    cmds:
      - cd packages/api && npx prisma generate

