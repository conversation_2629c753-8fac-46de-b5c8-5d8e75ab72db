# CLAUDE.md

This file provides guidance to Claude <PERSON> (claude.ai/code) when working with code in this repository.

## High-Level Architecture

This is a monorepo containing a full-stack application.

- **Frontend:** The frontend is a React application built with Vite, located in `packages/app`. It uses TanStack Router for routing. The main components are in `packages/app/src/components`. The router is configured in `packages/app/src/router.tsx`.

Please use pnpm as package manager kits.


## Use Taskfile as script entrypoint
