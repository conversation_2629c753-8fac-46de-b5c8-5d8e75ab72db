{"name": "@startwrite/web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"@ai-sdk/google": "^2.0.7", "@ai-sdk/react": "^2.0.15", "@startwrite/database": "workspace:*", "@startwrite/ui": "workspace:*", "@tiptap/extension-character-count": "^3.2.1", "@tiptap/extension-focus": "^3.2.1", "@tiptap/extension-placeholder": "^3.2.1", "@tiptap/react": "^3.2.1", "@tiptap/starter-kit": "^3.2.1", "ai": "^5.0.15", "better-auth": "^1.3.7", "clsx": "^2.1.1", "lucide-react": "^0.344.0", "next": "^15.5.0", "react": "^19.1.1", "react-dom": "^19.1.1", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@types/node": "^20.19.11", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "eslint": "^9.9.1", "eslint-config-next": "^15.5.0", "typescript": "^5.5.3"}}