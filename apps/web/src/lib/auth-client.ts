import { createAuthClient } from 'better-auth/react';

export const authClient = createAuthClient({
  baseURL: process.env.NEXT_PUBLIC_VERCEL_URL
    ? `https://${process.env.NEXT_PUBLIC_VERCEL_URL}`
    : 'http://localhost:3000',
  basePath: '/api/auth',
});

export const { useSession, signIn, signOut, signUp } = authClient;

// Create a useAuth hook that returns user and session info
export const useAuth = () => {
  const session = useSession();
  return {
    user: session.data?.user || null,
    session: session.data || null,
    isLoading: session.isPending,
    error: session.error,
  };
};
