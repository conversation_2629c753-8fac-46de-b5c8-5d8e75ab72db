import { create } from 'zustand';
import { immer } from 'zustand/middleware/immer';

// Represents the Prisma schema for an Article
export interface Article {
  id: string;
  title: string;
  content: string; // Storing content as a string, can be JSON or HTML
  authorId: string;
  createdAt: Date;
  updatedAt: Date;
  isPublished: boolean;
}

interface ArticleState {
  articles: Record<string, Article>;
  activeArticleId: string | null;
  isLoading: boolean;
  isSaving: boolean;
  error: string | null;
}

interface ArticleActions {
  createArticle: (authorId: string, initialContent?: Partial<Omit<Article, 'id' | 'authorId' | 'createdAt' | 'updatedAt'>>) => Promise<Article>;
  fetchArticle: (articleId: string) => Promise<void>;
  updateArticle: (articleId: string, updates: Partial<Omit<Article, 'id'>>) => Promise<void>;
  deleteArticle: (articleId: string) => Promise<void>;
  setActiveArticle: (articleId: string | null) => void;
}

export const useArticleStore = create(
  immer<ArticleState & ArticleActions>((set, get) => ({
    articles: {},
    activeArticleId: null,
    isLoading: false,
    isSaving: false,
    error: null,

    setActiveArticle: (articleId) => {
      set((state) => {
        state.activeArticleId = articleId;
      });
    },

    createArticle: async (authorId, initialContent = {}) => {
      set({ isSaving: true, error: null });
      try {
        const response = await fetch('/api/articles', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            authorId,
            title: initialContent.title || 'Untitled Article',
            content: initialContent.content || '',
            isPublished: initialContent.isPublished || false,
          }),
        });

        if (!response.ok) {
          throw new Error('Failed to create article');
        }

        const newArticle: Article = await response.json();
        
        set((state) => {
          state.articles[newArticle.id] = newArticle;
          state.isSaving = false;
        });

        return newArticle;
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
        set({ isSaving: false, error: errorMessage });
        throw error;
      }
    },

    fetchArticle: async (articleId) => {
      set({ isLoading: true, error: null });
      try {
        const response = await fetch(`/api/articles/${articleId}`);
        if (!response.ok) {
          throw new Error('Failed to fetch article');
        }
        const article: Article = await response.json();
        set((state) => {
          state.articles[article.id] = article;
          state.isLoading = false;
        });
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
        set({ isLoading: false, error: errorMessage });
      }
    },

    updateArticle: async (articleId, updates) => {
      set((state) => {
        state.isSaving = true;
        state.error = null;
        // Optimistic update
        if (state.articles[articleId]) {
          state.articles[articleId] = { ...state.articles[articleId], ...updates };
        }
      });

      try {
        const response = await fetch(`/api/articles/${articleId}`, {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(updates),
        });

        if (!response.ok) {
          throw new Error('Failed to update article');
        }

        const updatedArticle: Article = await response.json();
        
        set((state) => {
          state.articles[updatedArticle.id] = updatedArticle;
          state.isSaving = false;
        });
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
        // Revert optimistic update on failure if needed, or handle more gracefully
        set({ isSaving: false, error: errorMessage });
      }
    },

    deleteArticle: async (articleId) => {
      set({ isSaving: true, error: null });
      try {
        const response = await fetch(`/api/articles/${articleId}`, {
          method: 'DELETE',
        });

        if (!response.ok) {
          throw new Error('Failed to delete article');
        }

        set((state) => {
          delete state.articles[articleId];
          if (state.activeArticleId === articleId) {
            state.activeArticleId = null;
          }
          state.isSaving = false;
        });
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
        set({ isSaving: false, error: errorMessage });
        throw error;
      }
    },
  }))
);
