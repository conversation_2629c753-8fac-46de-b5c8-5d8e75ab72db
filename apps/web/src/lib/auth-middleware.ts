import { NextRequest } from 'next/server'
import { auth } from './auth'

export async function getAuthenticatedUser(request: NextRequest) {
  const cookieHeader = request.headers.get('cookie') || '';
  const headers = new Headers();
  headers.set('cookie', cookieHeader);
  
  const session = await auth.api.getSession({
    headers,
  });

  return session?.user || null;
}

export async function requireAuth(request: NextRequest) {
  const user = await getAuthenticatedUser(request);
  
  if (!user) {
    return Response.json({ error: 'Unauthorized' }, { status: 401 });
  }
  
  return user;
}
