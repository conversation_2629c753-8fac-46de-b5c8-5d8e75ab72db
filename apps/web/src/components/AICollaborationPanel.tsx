'use client'

import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>, Lightbulb, Edit, MessageCircle, ArrowUp } from 'lucide-react';

interface AICollaborationPanelProps {
  onClose: () => void;
}

export function AICollaborationPanel({ onClose }: AICollaborationPanelProps) {
  const [messages, setMessages] = useState<Array<{id: string, role: 'user' | 'assistant', content: string}>>([]);
  const [input, setInput] = useState('');

  const suggestions = [
    { icon: Lightbulb, text: 'Help me brainstorm ideas about...', prompt: 'Help me brainstorm ideas about' },
    { icon: Edit, text: 'Improve this paragraph', prompt: 'Can you help me improve this writing?' },
    { icon: MessageCircle, text: 'Ask me questions to explore this topic', prompt: 'Ask me questions to help me explore this topic deeper' },
  ];

  const handleSuggestionClick = (prompt: string) => {
    setInput(prompt);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!input.trim()) return;

    const userMessage = {
      id: Date.now().toString(),
      role: 'user' as const,
      content: input
    };

    setMessages(prev => [...prev, userMessage]);
    setInput('');

    try {
      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ messages: [...messages, userMessage] })
      });

      if (response.ok) {
        const reader = response.body?.getReader();
        if (reader) {
          const assistantMessage = {
            id: (Date.now() + 1).toString(),
            role: 'assistant' as const,
            content: ''
          };

          setMessages(prev => [...prev, assistantMessage]);

          while (true) {
            const { done, value } = await reader.read();
            if (done) break;

            const chunk = new TextDecoder().decode(value);
            assistantMessage.content += chunk;
            setMessages(prev => prev.map(msg =>
              msg.id === assistantMessage.id ? { ...msg, content: assistantMessage.content } : msg
            ));
          }
        }
      }
    } catch (error) {
      console.error('Error sending message:', error);
    }
  };

  return (
    <div className="fixed inset-y-0 right-0 w-96 bg-white border-l border-slate-200 shadow-2xl z-50 flex flex-col">
      <div className="flex items-center justify-between p-4 border-b border-slate-200">
        <div className="flex items-center space-x-2">
          <Sparkles className="text-indigo-600" size={20} />
          <h2 className="font-semibold text-slate-900">AI Collaboration</h2>
        </div>
        <button
          onClick={onClose}
          className="p-1 rounded-md text-slate-500 hover:text-slate-700 hover:bg-slate-100"
        >
          <X size={18} />
        </button>
      </div>

      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.map((message) => (
          <div
            key={message.id}
            className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
          >
            <div
              className={`max-w-[80%] p-3 rounded-xl ${
                message.role === 'user'
                  ? 'bg-indigo-600 text-white'
                  : 'bg-slate-100 text-slate-900'
              }`}
            >
              <p className="text-sm whitespace-pre-line">
                {message.content}
              </p>
            </div>
          </div>
        ))}

        {messages.length === 0 && (
          <div className="space-y-2">
            <p className="text-sm font-medium text-slate-700">Quick suggestions:</p>
            {suggestions.map((suggestion, index) => {
              const Icon = suggestion.icon;
              return (
                <button
                  key={index}
                  onClick={() => handleSuggestionClick(suggestion.prompt)}
                  className="w-full flex items-center space-x-3 p-3 rounded-lg border border-slate-200 hover:border-indigo-300 hover:bg-indigo-50 transition-all duration-200 text-left"
                >
                  <Icon className="text-indigo-600" size={16} />
                  <span className="text-sm text-slate-700">{suggestion.text}</span>
                </button>
              );
            })}
          </div>
        )}
      </div>

      <div className="p-4 border-t border-slate-200">
        <form onSubmit={handleSubmit}>
          <div className="relative">
            <textarea
              value={input}
              onChange={(e) => setInput(e.target.value)}
              placeholder="Ask the AI for help..."
              rows={1}
              className="w-full px-4 py-3 pr-12 border border-slate-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent resize-none"
              style={{ minHeight: '48px' }}
            />
            <button
              type="submit"
              disabled={!input || input.trim() === ''}
              className="absolute right-2 top-2 p-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
            >
              <ArrowUp size={16} />
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
