'use client'

import React from 'react';
import { PenTool, <PERSON><PERSON><PERSON>, Spark<PERSON>, Settings, LogIn } from 'lucide-react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';

interface NavigationProps {
  onToggleAI: () => void;
  onToggleWritingTools: () => void;
}

export function Navigation({ onToggleAI, onToggleWritingTools }: NavigationProps) {
  const pathname = usePathname();
  
  const navItems = [
    { href: '/notes', label: 'Notes', icon: PenTool },
    { href: '/articles', label: 'Articles', icon: BookOpen },
  ];

  const isActive = (href: string) => pathname === href;

  return (
    <nav className="bg-white border-b border-slate-200 px-6 py-4">
      <div className="max-w-7xl mx-auto flex items-center justify-between">
        <div className="flex items-center space-x-8">
          <Link href="/" className="text-xl font-semibold text-slate-900">
            Mindful
          </Link>
          
          <div className="hidden md:flex space-x-1">
            {navItems.map((item) => {
              const Icon = item.icon;
              return (
                <Link
                  key={item.href}
                  href={item.href}
                  className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-all duration-200 ${
                    isActive(item.href)
                      ? 'bg-indigo-50 text-indigo-700 font-medium'
                      : 'text-slate-600 hover:text-slate-900 hover:bg-slate-50'
                  }`}
                >
                  <Icon size={18} />
                  <span>{item.label}</span>
                </Link>
              );
            })}
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <Link
            href="/login"
            className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-all duration-200 ${
              isActive('/login')
                ? 'bg-indigo-50 text-indigo-700 font-medium'
                : 'text-slate-600 hover:text-indigo-700 hover:bg-indigo-50'
            }`}
          >
            <LogIn size={18} />
            <span className="hidden sm:inline">Login</span>
          </Link>
          <button
            onClick={onToggleAI}
            className="flex items-center space-x-2 px-4 py-2 rounded-lg text-slate-600 hover:text-indigo-700 hover:bg-indigo-50 transition-all duration-200"
          >
            <Sparkles size={18} />
            <span className="hidden sm:inline">AI Assist</span>
          </button>
          
          <button
            onClick={onToggleWritingTools}
            className="flex items-center space-x-2 px-4 py-2 rounded-lg text-slate-600 hover:text-slate-900 hover:bg-slate-50 transition-all duration-200"
          >
            <PenTool size={18} />
            <span className="hidden sm:inline">Tools</span>
          </button>
          
          <button className="p-2 rounded-lg text-slate-600 hover:text-slate-900 hover:bg-slate-50 transition-all duration-200">
            <Settings size={18} />
          </button>
        </div>
      </div>
    </nav>
  );
}
