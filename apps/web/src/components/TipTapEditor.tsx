import React, { useEffect, useState } from 'react';
import { useEditor, EditorContent } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import Placeholder from '@tiptap/extension-placeholder';
import CharacterCount from '@tiptap/extension-character-count';
import Focus from '@tiptap/extension-focus';
import Paragraph from '@tiptap/extension-paragraph';

const AiPromptParagraph = Paragraph.extend({
  addAttributes() {
    return {
      class: {
        default: null,
        parseHTML: (element) => element.getAttribute('class'),
        renderHTML: (attributes) => {
          if (!attributes.class) {
            return {};
          }
          return {
            class: attributes.class,
          };
        },
      },
    };
  },
});
import { <PERSON><PERSON><PERSON>, Loader2 } from 'lucide-react';

interface TipTapEditorProps {
  placeholder?: string;
  onContentChange?: (content: string) => void;
  initialContent?: string;
  showAIPrompts?: boolean;
}

export function TipTapEditor({
  placeholder = "Start writing...",
  onContentChange,
  initialContent = "",
  showAIPrompts = true
}: TipTapEditorProps) {
  const [isGenerating, setIsGenerating] = useState(false);

  const handlePromptClick = (promptText: string) => {
    if (!editor) return;

    const { tr } = editor.state;
    let from: number | undefined;
    let to: number | undefined;

    // Find the block of AI prompts
    editor.state.doc.descendants((node, pos) => {
      if (node.type.name === 'paragraph' && node.attrs.class?.includes('ai-prompt')) {
        if (from === undefined) from = pos;
        to = pos + node.nodeSize;
      }
    });

    // Expand selection to include the trailing empty paragraph for the placeholder
    if (to) {
      const resolvedPos = editor.state.doc.resolve(to);
      const nextNode = resolvedPos.nodeAfter;
      if (nextNode && nextNode.type.name === 'paragraph' && nextNode.content.size === 0) {
        to += nextNode.nodeSize;
      }
    }

    // If we found prompts, delete their range.
    if (from !== undefined && to !== undefined) {
      tr.delete(from, to);
    }
    
    const insertionPos = from !== undefined ? from : tr.doc.content.size;
    const pNode = editor.state.schema.nodes.paragraph;
    const textNode = editor.state.schema.text(promptText + ' ');
    
    tr.insert(insertionPos, pNode.create(null, textNode));
    
    const finalPos = tr.mapping.map(insertionPos) + textNode.nodeSize + 1;
    tr.insert(finalPos, pNode.create());

    editor.view.dispatch(tr);
    editor.chain().focus(finalPos).run();
  };

  const editor = useEditor({
    extensions: [
      StarterKit.configure({
        paragraph: false,
      }),
      AiPromptParagraph,
      Placeholder.configure({
        // Use contextual placeholders: initial vs. in-document
        placeholder: ({ editor }) =>
          editor?.state?.doc?.textContent?.length ? 'Continue your thoughts here...' : placeholder,
        showOnlyCurrent: true,
      }),
      CharacterCount,
      Focus.configure({
        className: 'has-focus',
        mode: 'all',
      }),
    ],
    content: initialContent,
    immediatelyRender: false,
    onUpdate: ({ editor }) => {
      const content = editor.getHTML();
      onContentChange?.(content);
    },
    editorProps: {
      attributes: {
        class: 'prose prose-slate max-w-none focus:outline-none min-h-[400px] p-4 prose-headings:text-slate-900 prose-p:text-slate-700 prose-strong:text-slate-900 prose-em:text-slate-700',
      },
      handleKeyDown: (view, event) => {
        // Handle Shift+Enter for AI continue writing
        if (event.key === 'Enter' && event.shiftKey) {
          event.preventDefault();
          handleContinueWriting();
          return true;
        }
        return false;
      },
    },
  });

  // Escape HTML for safe insertion into editor
  const escapeHtml = (str: string) =>
    str
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;');

  // Insert AI prompts at the end of the article with immersive styles
  const insertPromptsAtEnd = (raw: string) => {
    if (!editor) return;
    const prompts = raw
      .split(/\n+/)
      .map((s) => s.trim())
      .filter(Boolean);

    if (prompts.length === 0) return;

    const html =
      prompts.map((p) => `<p class="ai-prompt">${escapeHtml(p)}</p>`).join('') +
      `<p></p>`; // empty paragraph so the Placeholder shows "Write here.."

    const endPos = editor.state.doc.content.size;
    editor
      .chain()
      .focus()
      .insertContentAt(endPos, html)
      .run();
  };

  const handleContinueWriting = async () => {
    if (!editor || isGenerating) return;

    const currentContent = editor.getText();

    setIsGenerating(true);

    try {
      const response = await fetch('/api/continue', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ text: currentContent }),
      });

      if (!response.ok) {
        throw new Error('Failed to get AI suggestion');
      }

      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('No response body');
      }

      let suggestion = '';
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        const chunk = new TextDecoder().decode(value);
        suggestion += chunk;
      }

      // Append prompts to the bottom of the article
      insertPromptsAtEnd(suggestion);
    } catch (error) {
      console.error('Error getting AI suggestion:', error);
      insertPromptsAtEnd('Write a little more about what stands out to you.');
    } finally {
      setIsGenerating(false);
    }
  };

  useEffect(() => {
    if (editor && initialContent !== editor.getHTML()) {
      editor.commands.setContent(initialContent);
    }
  }, [editor, initialContent]);

  if (!editor) {
    return (
      <div className="border border-slate-200 rounded-lg p-4 min-h-[400px] flex items-center justify-center">
        <Loader2 className="animate-spin text-slate-400" size={24} />
      </div>
    );
  }

  return (
    <div className="relative" onClick={(e) => {
      const target = e.target as HTMLElement;
      if (target.classList.contains('ai-prompt')) {
        handlePromptClick(target.textContent || '');
      }
    }}>
      <div className="border border-slate-200 rounded-lg overflow-hidden focus-within:ring-2 focus-within:ring-indigo-500 focus-within:border-transparent">
        <EditorContent editor={editor} />

        {/* Character count and AI hint */}
        <div className="flex items-center justify-between px-4 py-2 bg-slate-50 border-t border-slate-200 text-sm text-slate-500">
          <div className="flex items-center space-x-4">
            <span>{editor.storage.characterCount.characters()} characters</span>
            <span>{editor.storage.characterCount.words()} words</span>
          </div>
          {showAIPrompts && (
            <div className="flex items-center space-x-2">
              <Sparkles size={14} className="text-indigo-500" />
              <span>{isGenerating ? 'Generating prompts…' : 'Shift+Enter to get prompts at the bottom'}</span>
            </div>
          )}
        </div>
      </div>

      {isGenerating && (
        <div className="pointer-events-none absolute left-3 bottom-12 z-10 flex items-center space-x-2 text-indigo-600 bg-white/70 backdrop-blur-sm rounded-full px-2 py-1 shadow-sm">
          <Loader2 className="animate-spin" size={16} />
          <span className="text-xs">Generating tips…</span>
        </div>
      )}

      {/* Immersive prompt styles */}
      <style jsx global>{`
        @keyframes fadeIn {
          from { opacity: 0; transform: translateY(3px); }
          to { opacity: 1; transform: translateY(0); }
        }

        /* Make reading/writing feel immersive */
        .ProseMirror { font-size: 1.05rem; line-height: 1.85; letter-spacing: 0.1px; }
        .ProseMirror p { margin: 0.5rem 0; }

        /* AI prompt look */
        .ProseMirror p.ai-prompt {
          display: inline-flex;
          align-items: center;
          padding: 0.3rem 0.8rem;
          border-radius: 9999px;
          background-color: #eef2ff;
          color: #4f46e5;
          font-size: 0.95rem;
          cursor: pointer;
          transition: all 0.2s ease-in-out;
          margin: 0.25rem 0.35rem 0.25rem 0;
          border-left: none;
          line-height: 1.5;
          animation: fadeIn 0.4s ease-out forwards;
        }
        .ProseMirror p.ai-prompt::before {
          content: '✨';
          margin-right: 0.5rem;
          opacity: 0.9;
        }
        .ProseMirror p.ai-prompt:hover {
          background-color: #e0e7ff;
          color: #3730a3;
          transform: translateY(-1px);
          box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }
        .ProseMirror p.ai-prompt + p.ai-prompt { margin-top: 0; }

        /* Tiptap Placeholder tweak to look like a subtle input */
        .ProseMirror p.is-editor-empty:first-child::before,
        .ProseMirror p:has(br).is-editor-empty::before {
          color: #9ca3af; /* gray-400 - more neutral than slate */
          content: attr(data-placeholder);
          float: left;
          height: 0;
          pointer-events: none;
          font-style: italic;
          font-weight: 300;
          opacity: 0.8;
        }

        /* Focus ring inside editor */
        .has-focus { outline: none; box-shadow: inset 0 0 0 2px rgba(99,102,241,0.25); }
      `}</style>
    </div>
  );
}
