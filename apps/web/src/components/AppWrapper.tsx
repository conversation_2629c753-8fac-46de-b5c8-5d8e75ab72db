'use client'

import React, { useState } from 'react';
import { Navigation } from './Navigation';
import { AICollaborationPanel } from './AICollaborationPanel';
import { WritingToolsSidebar } from './WritingToolsSidebar';

interface AppWrapperProps {
  children: React.ReactNode;
}

export function AppWrapper({ children }: AppWrapperProps) {
  const [isAIPanelOpen, setIsAIPanelOpen] = useState(false);
  const [isWritingToolsOpen, setIsWritingToolsOpen] = useState(false);

  return (
    <div className="min-h-screen bg-slate-50 flex flex-col">
      <Navigation
        onToggleAI={() => setIsAIPanelOpen(!isAIPanelOpen)}
        onToggleWritingTools={() => setIsWritingToolsOpen(!isWritingToolsOpen)}
      />
      <div className="flex-1 flex">
        <main className="flex-1 relative">
          {children}
        </main>
        
        {isAIPanelOpen && (
          <AICollaborationPanel onClose={() => setIsAIPanelOpen(false)} />
        )}
        
        {isWritingToolsOpen && (
          <WritingToolsSidebar onClose={() => setIsWritingToolsOpen(false)} />
        )}
      </div>
    </div>
  );
}
