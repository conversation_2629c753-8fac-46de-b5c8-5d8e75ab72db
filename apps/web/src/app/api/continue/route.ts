import { streamText } from 'ai'
import { google } from '@ai-sdk/google'
import { NextRequest } from 'next/server'

const model = google('gemini-2.5-pro')

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { text } = body as { text: string }

    const prompt = `
Help user keep writing; provide next prompt guides which could help user writing. Ask a direct question that prompts the user's next writing action based on their last text. Do not write new content; only ask or give a brief nudge. If no context is provided, ask what they are writing and their next goal. Always return one or two sentences;
the user's last text: ${text}
`

    const result = await streamText({
      model,
      prompt,
    })

    return result.toTextStreamResponse()
  } catch (error: unknown) {
    console.error('Error in /api/continue:', error)
    return Response.json({ 
      error: 'Internal Server Error', 
      message: error instanceof Error ? error.message : 'Unknown error' 
    }, { status: 500 })
  }
}
