import { auth } from '@/lib/auth'
import { NextRequest } from 'next/server'

export async function GET(request: NextRequest) {
  console.log('Auth GET handler received path:', request.nextUrl.pathname);
  try {
    const response = await auth.handler(request);
    return response;
  } catch (error) {
    console.error('Error in auth GET handler:', error);
    return Response.json(
      { 
        error: 'Internal Server Error in auth handler', 
        message: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  console.log('Auth POST handler received path:', request.nextUrl.pathname);
  try {
    const response = await auth.handler(request);
    return response;
  } catch (error) {
    console.error('Error in auth POST handler:', error);
    return Response.json(
      { 
        error: 'Internal Server Error in auth handler', 
        message: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    );
  }
}
