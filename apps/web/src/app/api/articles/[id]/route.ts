import { NextRequest, NextResponse } from 'next/server';
import { requireAuth } from '@/lib/auth-middleware';
import { prisma } from '@/lib/prisma';

async function authorizeArticleAccess(
  request: NextRequest,
  articleId: string
) {
  const user = await requireAuth(request);
  if (user instanceof Response) {
    return { user, article: null, error: user };
  }

  const article = await prisma.article.findUnique({
    where: { id: articleId },
  });

  if (!article || article.authorId !== user.id) {
    const errorResponse = NextResponse.json(
      { error: 'Article not found or access denied' },
      { status: 404 }
    );
    return { user, article: null, error: errorResponse };
  }

  return { user, article, error: null };
}

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  const { article, error } = await authorizeArticleAccess(request, params.id);
  if (error) {
    return error;
  }
  return NextResponse.json(article);
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  const { article, error } = await authorizeArticleAccess(request, params.id);
  if (error) {
    return error;
  }

  try {
    const { title, content, isPublished } = await request.json();
    const updatedArticle = await prisma.article.update({
      where: { id: params.id },
      data: {
        title: title ?? article.title,
        content: content ?? article.content,
        isPublished: isPublished ?? article.isPublished,
      },
    });
    return NextResponse.json(updatedArticle);
  } catch (e) {
    console.error('Error updating article:', e);
    return NextResponse.json(
      { error: 'Failed to update article' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  const { error } = await authorizeArticleAccess(request, params.id);
  if (error) {
    return error;
  }

  try {
    await prisma.article.delete({
      where: { id: params.id },
    });
    return NextResponse.json({ message: 'Article deleted successfully' });
  } catch (e) {
    console.error('Error deleting article:', e);
    return NextResponse.json(
      { error: 'Failed to delete article' },
      { status: 500 }
    );
  }
}
