import { NextRequest, NextResponse } from 'next/server';
import { requireAuth } from '@/lib/auth-middleware';
import { prisma } from '@/lib/prisma';

export async function GET(request: NextRequest) {
  const user = await requireAuth(request);
  if (user instanceof Response) {
    return user;
  }

  const articles = await prisma.article.findMany({
    where: { authorId: user.id },
  });

  return NextResponse.json(articles);
}

export async function POST(request: NextRequest) {
  const user = await requireAuth(request);
  if (user instanceof Response) {
    return user;
  }

  try {
    const { title, content } = await request.json();
    const newArticle = await prisma.article.create({
      data: {
        title,
        content,
        authorId: user.id,
      },
    });
    return NextResponse.json(newArticle, { status: 201 });
  } catch (error) {
    console.error('Error creating article:', error);
    return NextResponse.json(
      { error: 'Failed to create article' },
      { status: 500 }
    );
  }
}
