import { streamText, convertToModelMessages, type UIMessage } from 'ai'
import { google } from '@ai-sdk/google'
import { NextRequest } from 'next/server'

const model = google('gemini-2.5-pro')

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { messages } = body as { messages: Array<Omit<UIMessage, 'id'>> }

    const result = await streamText({
      model,
      messages: convertToModelMessages(messages),
    })

    return result.toUIMessageStreamResponse()
  } catch (error: unknown) {
    console.error('Error in /api/chat:', error)
    return Response.json({ 
      error: 'Internal Server Error', 
      message: error instanceof Error ? error.message : 'Unknown error' 
    }, { status: 500 })
  }
}
