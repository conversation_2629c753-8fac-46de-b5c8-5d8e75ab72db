'use client'

import React, { useState } from 'react';
import { TipTapEditor } from '@/components/TipTapEditor';

export default function TestEditorPage() {
  const [content, setContent] = useState('');

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-slate-900 mb-2">TipTap Editor Test</h1>
        <p className="text-slate-600">
          Test the TipTap Editor with AI continue writing functionality. 
          Press <kbd className="px-2 py-1 bg-slate-100 rounded text-sm">Shift+Enter</kbd> to get AI writing suggestions.
        </p>
      </div>

      <div className="space-y-6">
        <TipTapEditor
          placeholder="Start writing your test content... (Press Shift+Enter for AI assistance)"
          onContentChange={setContent}
          initialContent=""
          showAIPrompts={true}
        />

        {content && (
          <div className="mt-8">
            <h2 className="text-xl font-semibold text-slate-900 mb-4">Raw HTML Output:</h2>
            <pre className="bg-slate-100 p-4 rounded-lg text-sm overflow-auto">
              {content}
            </pre>
          </div>
        )}
      </div>
    </div>
  );
}
