'use client'

import React, { useState } from 'react';
import { Plus, Search, Tag, Calendar, Star } from 'lucide-react';
import { Note } from '@/lib/types';
import { formatDate } from '@/lib/utils';

export default function NotesPage() {
  const [notes] = useState<Note[]>([
    {
      id: '1',
      title: 'Meeting Ideas',
      content: 'Key insights from today\'s brainstorming session about user experience improvements...',
      tags: ['work', 'ideas'],
      createdAt: new Date(2024, 0, 15),
      isStarred: true
    },
    {
      id: '2',
      title: 'Book Notes: Deep Work',
      content: 'The ability to focus without distraction on a cognitively demanding task...',
      tags: ['reading', 'productivity'],
      createdAt: new Date(2024, 0, 14),
      isStarred: false
    },
    {
      id: '3',
      title: 'Project Roadmap',
      content: 'Q1 objectives and key milestones for the new product launch...',
      tags: ['planning', 'work'],
      createdAt: new Date(2024, 0, 13),
      isStarred: true
    }
  ]);

  const [searchQuery, setSearchQuery] = useState('');
  const [selectedNote, setSelectedNote] = useState<Note | null>(null);
  const [isCreatingNote, setIsCreatingNote] = useState(false);

  const handleCreateNote = () => {
    setIsCreatingNote(true);
    setSelectedNote(null);
  };

  const filteredNotes = notes.filter(note =>
    note.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    note.content.toLowerCase().includes(searchQuery.toLowerCase()) ||
    note.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
  );

  return (
    <div className="h-full flex">
      {/* Sidebar */}
      <div className="w-80 bg-white border-r border-slate-200 flex flex-col">
        <div className="p-4 border-b border-slate-200">
          <div className="flex items-center justify-between mb-4">
            <h1 className="text-xl font-semibold text-slate-900">Notes</h1>
            <button
              onClick={handleCreateNote}
              className="p-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors duration-200"
            >
              <Plus size={18} />
            </button>
          </div>
          
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400" size={18} />
            <input
              type="text"
              placeholder="Search notes..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-slate-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
            />
          </div>
        </div>

        <div className="flex-1 overflow-y-auto">
          {filteredNotes.map((note) => (
            <div
              key={note.id}
              onClick={() => setSelectedNote(note)}
              className={`p-4 border-b border-slate-100 cursor-pointer hover:bg-slate-50 transition-colors duration-200 ${
                selectedNote?.id === note.id ? 'bg-indigo-50 border-indigo-200' : ''
              }`}
            >
              <div className="flex items-start justify-between mb-2">
                <h3 className="font-medium text-slate-900 truncate">{note.title}</h3>
                {note.isStarred && <Star className="text-amber-500 fill-current" size={16} />}
              </div>
              
              <p className="text-sm text-slate-600 mb-3 line-clamp-2">{note.content}</p>
              
              <div className="flex items-center justify-between text-xs text-slate-500">
                <div className="flex items-center space-x-1">
                  <Calendar size={12} />
                  <span>{formatDate(note.createdAt)}</span>
                </div>
                
                {note.tags.length > 0 && (
                  <div className="flex items-center space-x-1">
                    <Tag size={12} />
                    <span>{note.tags[0]}</span>
                    {note.tags.length > 1 && <span>+{note.tags.length - 1}</span>}
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col">
        {selectedNote ? (
          <div className="h-full flex flex-col">
            <div className="p-6 border-b border-slate-200">
              <h1 className="text-2xl font-bold text-slate-900 mb-2">{selectedNote.title}</h1>
              <div className="flex items-center space-x-4 text-sm text-slate-600">
                <div className="flex items-center space-x-1">
                  <Calendar size={14} />
                  <span>{formatDate(selectedNote.createdAt)}</span>
                </div>
                {selectedNote.tags.length > 0 && (
                  <div className="flex items-center space-x-2">
                    <Tag size={14} />
                    <div className="flex space-x-1">
                      {selectedNote.tags.map((tag) => (
                        <span
                          key={tag}
                          className="px-2 py-1 bg-slate-100 text-slate-700 rounded-md text-xs"
                        >
                          {tag}
                        </span>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
            <div className="flex-1 p-6">
              <div className="prose max-w-none">
                {selectedNote.content}
              </div>
            </div>
          </div>
        ) : isCreatingNote ? (
          <div className="h-full flex flex-col">
            <div className="p-6 border-b border-slate-200">
              <h1 className="text-2xl font-bold text-slate-900">New Note</h1>
            </div>
            <div className="flex-1 p-6">
              <p className="text-slate-600">Start writing your new note...</p>
            </div>
          </div>
        ) : (
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center">
              <h2 className="text-xl font-semibold text-slate-900 mb-2">Select a note to view</h2>
              <p className="text-slate-600 mb-4">Choose a note from the sidebar or create a new one</p>
              <button
                onClick={handleCreateNote}
                className="inline-flex items-center space-x-2 px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors duration-200"
              >
                <Plus size={18} />
                <span>Create New Note</span>
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
