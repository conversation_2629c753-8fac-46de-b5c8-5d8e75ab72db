'use client'

import React, { useEffect, useState } from 'react';
import Link from 'next/link';
import { Plus, Calendar, Edit, Trash2 } from 'lucide-react';
import { Article } from '@/lib/types';
import { formatDate } from '@/lib/utils';

export default function ArticlesPage() {
  const [articles, setArticles] = useState<Article[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchArticles = async () => {
      try {
        const response = await fetch('/api/articles');
        if (response.ok) {
          const data = await response.json();
          setArticles(data);
        }
      } catch (error) {
        console.error('Error fetching articles:', error);
      } finally {
        setLoading(false);
      }
    };
    fetchArticles();
  }, []);

  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this article?')) return;
    
    try {
      const response = await fetch(`/api/articles/${id}`, {
        method: 'DELETE',
      });
      
      if (response.ok) {
        setArticles(articles.filter((a) => a.id !== id));
      }
    } catch (error) {
      console.error('Error deleting article:', error);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-slate-600">Loading articles...</div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="flex items-center justify-between mb-8">
        <h1 className="text-3xl font-bold text-slate-900">Articles</h1>
        <Link
          href="/articles/new"
          className="inline-flex items-center space-x-2 px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors duration-200"
        >
          <Plus size={18} />
          <span>New Article</span>
        </Link>
      </div>

      {articles.length === 0 ? (
        <div className="text-center py-12">
          <h2 className="text-xl font-semibold text-slate-900 mb-2">No articles yet</h2>
          <p className="text-slate-600 mb-6">Start writing your first article</p>
          <Link
            href="/articles/new"
            className="inline-flex items-center space-x-2 px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors duration-200"
          >
            <Plus size={18} />
            <span>Create Article</span>
          </Link>
        </div>
      ) : (
        <div className="space-y-6">
          {articles.map((article) => (
            <div
              key={article.id}
              className="bg-white border border-slate-200 rounded-lg p-6 hover:shadow-md transition-shadow duration-200"
            >
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <h2 className="text-xl font-semibold text-slate-900 mb-2">
                    <Link
                      href={`/articles/${article.id}`}
                      className="hover:text-indigo-600 transition-colors duration-200"
                    >
                      {article.title}
                    </Link>
                  </h2>
                  
                  <p className="text-slate-600 mb-4 line-clamp-3">
                    {article.content.substring(0, 200)}...
                  </p>
                  
                  <div className="flex items-center space-x-4 text-sm text-slate-500">
                    <div className="flex items-center space-x-1">
                      <Calendar size={14} />
                      <span>Created {formatDate(article.createdAt)}</span>
                    </div>
                    {article.updatedAt !== article.createdAt && (
                      <div className="flex items-center space-x-1">
                        <Edit size={14} />
                        <span>Updated {formatDate(article.updatedAt)}</span>
                      </div>
                    )}
                  </div>
                </div>
                
                <div className="flex items-center space-x-2 ml-4">
                  <Link
                    href={`/articles/${article.id}`}
                    className="p-2 text-slate-600 hover:text-indigo-600 hover:bg-indigo-50 rounded-lg transition-colors duration-200"
                    title="Edit article"
                  >
                    <Edit size={18} />
                  </Link>
                  <button
                    onClick={() => handleDelete(article.id)}
                    className="p-2 text-slate-600 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors duration-200"
                    title="Delete article"
                  >
                    <Trash2 size={18} />
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
