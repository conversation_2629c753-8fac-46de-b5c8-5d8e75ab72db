'use client'

import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { Save, ArrowLeft, Trash2 } from 'lucide-react';
import Link from 'next/link';
import { useArticleStore, Article } from '@/lib/articles-store';
import { TipTapEditor } from '@/components/TipTapEditor';

function EditArticleForm({ article }: { article: Article }) {
  const router = useRouter();
  const { updateArticle, deleteArticle, isSaving } = useArticleStore();

  const [title, setTitle] = useState(article.title);
  const [content, setContent] = useState(article.content);

  const handleSave = async () => {
    if (!title.trim() || !content.trim()) {
      alert('Please fill in both title and content');
      return;
    }

    try {
      await updateArticle(article.id, { title, content });
      alert('Article saved successfully!');
    } catch (error) {
      console.error('Error saving article:', error);
      alert('Failed to save article');
    }
  };

  const handleDelete = async () => {
    if (!confirm('Are you sure you want to delete this article?')) return;

    try {
      await deleteArticle(article.id);
      router.push('/articles');
    } catch (error) {
      console.error('Error deleting article:', error);
      alert('Failed to delete article');
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="flex items-center justify-between mb-8">
        <div className="flex items-center space-x-4">
          <Link
            href="/articles"
            className="p-2 text-slate-600 hover:text-slate-900 hover:bg-slate-100 rounded-lg transition-colors duration-200"
          >
            <ArrowLeft size={20} />
          </Link>
          <h1 className="text-3xl font-bold text-slate-900">Edit Article</h1>
        </div>
        
        <div className="flex items-center space-x-2">
          <button
            onClick={handleDelete}
            className="inline-flex items-center space-x-2 px-4 py-2 border border-red-300 text-red-700 rounded-lg hover:bg-red-50 transition-colors duration-200"
          >
            <Trash2 size={18} />
            <span>Delete</span>
          </button>
          <button
            onClick={handleSave}
            disabled={isSaving || !title.trim() || !content.trim()}
            className="inline-flex items-center space-x-2 px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
          >
            <Save size={18} />
            <span>{isSaving ? 'Saving...' : 'Save Changes'}</span>
          </button>
        </div>
      </div>

      <div className="space-y-6">
        <div>
          <label htmlFor="title" className="block text-sm font-medium text-slate-700 mb-2">
            Title
          </label>
          <input
            type="text"
            id="title"
            value={title}
            onChange={(e) => setTitle(e.target.value)}
            placeholder="Enter article title..."
            className="w-full px-4 py-3 border border-slate-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent text-lg"
          />
        </div>

        <div>
          <label htmlFor="content" className="block text-sm font-medium text-slate-700 mb-2">
            Content
          </label>
          <TipTapEditor
            placeholder="Start writing your article... (Press Shift+Enter for AI assistance)"
            onContentChange={setContent}
            initialContent={content}
            showAIPrompts={true}
          />
        </div>
      </div>
    </div>
  );
}

export default function EditArticlePage() {
  const params = useParams();
  const articleId = params.id as string;

  const { articles, fetchArticle, isLoading } = useArticleStore();
  const article = articles[articleId];

  useEffect(() => {
    if (articleId && !article) {
      fetchArticle(articleId);
    }
  }, [articleId, article, fetchArticle]);

  if (isLoading && !article) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-slate-600">Loading article...</div>
      </div>
    );
  }

  if (!article) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-slate-600">Article not found</div>
      </div>
    );
  }

  return <EditArticleForm article={article} />;
}
