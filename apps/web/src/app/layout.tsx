import type { Metadata } from 'next'
import './globals.css'
import { AppWrapper } from '@/components/AppWrapper'

export const metadata: Metadata = {
  title: 'Mindful - AI Collaboration & Note-Taking',
  description: 'A minimalist productivity app for AI collaboration, note-taking, and writing tools that promotes creativity and focus.',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <body className="antialiased">
        <AppWrapper>
          {children}
        </AppWrapper>
      </body>
    </html>
  )
}
